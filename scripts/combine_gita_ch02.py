import re
import json

def extract_array(ts_content, array_name):
    # Find the array assignment
    array_match = re.search(rf'{array_name}\s*[:=][^\[]*\[([\s\S]*?)\];', ts_content)
    if not array_match:
        raise ValueError(f"Array {array_name} not found")
    array_str = array_match.group(1)
    # Split into objects (naive, assumes no nested braces)
    objects = re.findall(r'\{[\s\S]*?\}', array_str)
    return objects

def parse_ts_object(obj_str):
    # Replace single quotes with double quotes, fix trailing commas
    obj_str = re.sub(r'(\w+):', r'"\1":', obj_str)
    obj_str = obj_str.replace("'", '"')
    obj_str = re.sub(r',\s*}', '}', obj_str)
    obj_str = re.sub(r',\s*\]', ']', obj_str)
    # Remove comments
    obj_str = re.sub(r'//.*', '', obj_str)
    # Remove trailing commas
    obj_str = re.sub(r',\s*\}', '}', obj_str)
    # Try to parse as JSON
    try:
        return json.loads(obj_str)
    except Exception as e:
        # Try to fix common issues
        obj_str = re.sub(r'(\w+):', r'"\1":', obj_str)
        return json.loads(obj_str)

def main():
    # Read files
    with open('../src/services/gita_ch02.ts', 'r', encoding='utf-8') as f:
        gita_ch02_ts = f.read()
    with open('../src/services/gita_ch02_insights.ts', 'r', encoding='utf-8') as f:
        gita_ch02_insights_ts = f.read()

    # Extract arrays
    gita_ch02_objs = extract_array(gita_ch02_ts, 'gita_ch02')
    gita_ch02_insights_objs = extract_array(gita_ch02_insights_ts, 'gita_ch02_insights')

    # Parse objects
    gita_ch02 = [parse_ts_object(obj) for obj in gita_ch02_objs]
    gita_ch02_insights = [parse_ts_object(obj) for obj in gita_ch02_insights_objs]

    # Build lookup for insights
    insights_lookup = {(v['chapter'], v['verse']): v for v in gita_ch02_insights}

    # Combine
    combined = []
    for verse in gita_ch02:
        key = (verse['chapter'], verse['verse'])
        insight = insights_lookup.get(key, {})
        combined.append({
            'chapter': verse['chapter'],
            'verse': verse['verse'],
            'text': verse['text'],
            'importance': verse['importance'],
            'translation': verse['translation'],
            'translation_jp': verse['translation_jp'],
            'insights': insight.get('insights', ''),
            'insights_jp': insight.get('insights_jp', ''),
        })

    # Write to file
    with open('gita_ch02_combined.json.txt', 'w', encoding='utf-8') as f:
        json.dump(combined, f, ensure_ascii=False, indent=2)

if __name__ == '__main__':
    main()