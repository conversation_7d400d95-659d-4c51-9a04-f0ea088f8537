import re
import json

def extract_array(file_path, array_name):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    # Find the array definition
    pattern = rf'{array_name}\s*=\s*\[(.*?)\];'
    match = re.search(pattern, content, re.DOTALL)
    if not match:
        raise ValueError(f"Array {array_name} not found in {file_path}")
    array_str = match.group(1)
    # Replace single quotes with double quotes for JSON compatibility
    array_str = array_str.replace("'", '"')
    # Remove trailing commas before closing braces
    array_str = re.sub(r',\s*}', '}', array_str)
    array_str = re.sub(r',\s*\]', ']', array_str)
    # Parse as JSON array
    array_json = json.loads(f'[{array_str}]')
    return array_json

def combine_arrays(ch02, ch02_insights):
    combined = []
    for verse, insight in zip(ch02, ch02_insights):
        combined.append({
            "chapter": verse.get("chapter"),
            "verse": verse.get("verse"),
            "text": verse.get("text"),
            "importance": verse.get("importance"),
            "translation": verse.get("translation"),
            "translation_jp": verse.get("translation_jp"),
            "insights": insight.get("insights"),
            "insights_jp": insight.get("insights_jp"),
        })
    return combined

if __name__ == "__main__":
    ch02 = extract_array("src/services/gita_ch02.ts", "gita_ch02")
    ch02_insights = extract_array("src/services/gita_ch02_insights.ts", "gita_ch02_insights")
    combined = combine_arrays(ch02, ch02_insights)
    with open("combined_ch02.json", "w", encoding="utf-8") as f:
        json.dump(combined, f, ensure_ascii=False, indent=2)